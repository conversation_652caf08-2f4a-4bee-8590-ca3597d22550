"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { AssetOperationFormBuilder } from "@/components/form-builder/asset-operation-form-builder";
import { FormDefinition } from "@/components/form-builder";
import { AssetOperationType } from "@/lib/types/asset-type-forms";
import { CustomField, DepreciationSettings, LifecycleStage, MaintenanceSchedule } from "@/lib/modules/asset-types/types";
import { AssetTypeConfigNavigation } from "@/components/asset-types/asset-type-config-navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Settings,
  FileText,
  TrendingDown,
  GitBranch,
  Wrench,
  Plus,
  Edit,
  Trash2,
  Save,
  AlertCircle,
  CheckCircle,
  Info,
  Eye
} from "lucide-react";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  categoryId: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
  depreciationSettings?: DepreciationSettings;
  lifecycleStages: LifecycleStage[];
  maintenanceSchedules: MaintenanceSchedule[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AssetTypeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("forms");
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "saved" | "error">("idle");

  useEffect(() => {
    loadAssetType();
  }, [assetTypeId]);

  const loadAssetType = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }

      const data = await response.json();
      setAssetType(data);

    } catch (error) {
      console.error("Error loading asset type:", error);
      setError("Failed to load asset type. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSave = async (form: FormDefinition, operationType: AssetOperationType) => {
    try {
      setIsSaving(true);
      setSaveStatus("saving");

      // First save the form definition
      const formResponse = await fetch("/api/form-definitions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: form.name,
          description: form.description,
          sections: form.sections,
          settings: form.settings,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!formResponse.ok) {
        throw new Error("Failed to save form definition");
      }

      const savedForm = await formResponse.json();

      // Then associate it with the asset type
      const associationResponse = await fetch("/api/asset-type-forms", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assetTypeId,
          formId: savedForm.id,
          operationType,
          isDefault: true,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!associationResponse.ok) {
        throw new Error("Failed to associate form with asset type");
      }

      setSaveStatus("saved");
      setTimeout(() => setSaveStatus("idle"), 2000);

    } catch (error) {
      console.error("Error saving form:", error);
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleFormPreview = (form: FormDefinition, operationType: AssetOperationType) => {
    // Open preview modal or navigate to preview page
    console.log("Preview form:", form, operationType);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset type...</span>
        </div>
      </div>
    );
  }

  if (error || !assetType) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || "Asset type not found"}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold">{assetType.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline">{assetType.code}</Badge>
              <Badge variant={assetType.isActive ? "default" : "secondary"}>
                {assetType.isActive ? "Active" : "Inactive"}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {assetType.category.name}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Details
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Description */}
      {assetType.description && (
        <Card>
          <CardContent className="p-4">
            <p className="text-muted-foreground">{assetType.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Configuration Navigation */}
      <AssetTypeConfigNavigation
        assetTypeId={assetTypeId}
        assetTypeName={assetType.name}
        stats={{
          forms: 0, // TODO: Get actual counts
          customFields: assetType.customFields?.length || 0,
          lifecycleStages: assetType.lifecycleStages?.length || 0,
          maintenanceSchedules: assetType.maintenanceSchedules?.length || 0,
          hasDepreciationSettings: !!assetType.depreciationSettings,
          workflows: 0, // TODO: Get actual count
        }}
      />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="forms" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Forms
          </TabsTrigger>
          <TabsTrigger value="depreciation" className="flex items-center gap-2">
            <TrendingDown className="h-4 w-4" />
            Depreciation
          </TabsTrigger>
          <TabsTrigger value="lifecycle" className="flex items-center gap-2">
            <GitBranch className="h-4 w-4" />
            Lifecycle
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Maintenance
          </TabsTrigger>
          <TabsTrigger value="fields" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Fields
          </TabsTrigger>
        </TabsList>

        {/* Forms Tab */}
        <TabsContent value="forms" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Forms Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure dynamic forms for different asset operations like creation, updates, maintenance logging, and more.
                </p>
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/forms`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Forms
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Forms
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Depreciation Tab */}
        <TabsContent value="depreciation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5" />
                Depreciation Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure depreciation methods, useful life, salvage values, and calculation parameters for this asset type.
                </p>
                {assetType.depreciationSettings && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted rounded-lg">
                    <div>
                      <label className="text-sm font-medium">Method</label>
                      <p className="text-sm text-muted-foreground capitalize">
                        {assetType.depreciationSettings.method?.replace('_', ' ') || 'Not configured'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Useful Life</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.usefulLife || 'Not set'} years
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Status</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.isActive ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/depreciation`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Depreciation
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Calculation
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lifecycle Tab */}
        <TabsContent value="lifecycle" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Lifecycle Stages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure lifecycle stages to track asset progression from acquisition to disposal.
                </p>
                {assetType.lifecycleStages && assetType.lifecycleStages.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Stages: {assetType.lifecycleStages.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.lifecycleStages
                        .sort((a, b) => a.order - b.order)
                        .map((stage) => (
                          <Badge key={stage.id} variant="outline" className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: stage.color }}
                            />
                            {stage.name}
                          </Badge>
                        ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/lifecycle`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Lifecycle
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Flow
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Maintenance Schedules
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure maintenance schedules to automate preventive, predictive, and corrective maintenance for this asset type.
                </p>
                {assetType.maintenanceSchedules && assetType.maintenanceSchedules.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Schedules: {assetType.maintenanceSchedules.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.maintenanceSchedules.map((schedule) => (
                        <Badge key={schedule.id} variant="outline">
                          {schedule.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/maintenance`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Maintenance
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Schedules
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Fields Tab */}
        <TabsContent value="fields" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Fields
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure custom fields to capture additional information specific to this asset type.
                </p>
                {assetType.customFields && assetType.customFields.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Fields: {assetType.customFields.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.customFields.map((field) => (
                        <Badge key={field.id} variant="outline" className="flex items-center gap-1">
                          {field.isRequired && <span className="text-red-500">*</span>}
                          {field.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/fields`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Fields
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Form
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}