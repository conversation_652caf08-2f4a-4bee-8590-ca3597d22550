import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { HeaderConfig } from "@/types/header";
import { 
  ArrowLeft, 
  Save, 
  Settings, 
  FileText, 
  GitBranch, 
  Wrench, 
  TrendingDown, 
  Workflow,
  Plus,
  Download,
  Upload
} from "lucide-react";

// Asset Type Forms Configuration Header
export const getAssetTypeFormsHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void
): HeaderConfig => ({
  title: "Forms Configuration",
  description: `Configure dynamic forms for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Forms", url: `/admin/asset-types/${assetTypeId}/forms` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});

// Custom Fields Configuration Header
export const getCustomFieldsHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void,
  onAddField?: () => void
): HeaderConfig => ({
  title: "Custom Fields Configuration",
  description: `Configure custom fields for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Custom Fields", url: `/admin/asset-types/${assetTypeId}/fields` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button variant="outline" key="add-field" onClick={onAddField}>
      <Plus className="mr-2 h-4 w-4" />
      Add Field
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});

// Lifecycle Stages Configuration Header
export const getLifecycleStagesHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void,
  onAddStage?: () => void
): HeaderConfig => ({
  title: "Lifecycle Stages Configuration",
  description: `Configure lifecycle stages for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Lifecycle", url: `/admin/asset-types/${assetTypeId}/lifecycle` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button variant="outline" key="add-stage" onClick={onAddStage}>
      <Plus className="mr-2 h-4 w-4" />
      Add Stage
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});

// Maintenance Schedules Configuration Header
export const getMaintenanceSchedulesHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void,
  onAddSchedule?: () => void
): HeaderConfig => ({
  title: "Maintenance Schedules Configuration",
  description: `Configure maintenance schedules for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Maintenance", url: `/admin/asset-types/${assetTypeId}/maintenance` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button variant="outline" key="add-schedule" onClick={onAddSchedule}>
      <Plus className="mr-2 h-4 w-4" />
      Add Schedule
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});

// Depreciation Settings Configuration Header
export const getDepreciationSettingsHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void
): HeaderConfig => ({
  title: "Depreciation Settings Configuration",
  description: `Configure depreciation settings for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Depreciation", url: `/admin/asset-types/${assetTypeId}/depreciation` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});

// Workflows Configuration Header
export const getWorkflowsHeaderConfig = (
  assetTypeId: string,
  assetTypeName: string,
  onSave?: () => void,
  onBack?: () => void,
  onAddWorkflow?: () => void
): HeaderConfig => ({
  title: "Workflows Configuration",
  description: `Configure automated workflows for ${assetTypeName}`,
  breadcrumbs: [
    { label: "Admin", url: "/admin" },
    { label: "Asset Types", url: "/admin/asset-types" },
    { label: assetTypeName, url: `/admin/asset-types/${assetTypeId}` },
    { label: "Workflows", url: `/admin/asset-types/${assetTypeId}/workflows` },
  ],
  variant: 'settings',
  actions: [
    <Button variant="outline" key="back" onClick={onBack}>
      <ArrowLeft className="mr-2 h-4 w-4" />
      Back
    </Button>,
    <Button variant="outline" key="import">
      <Upload className="mr-2 h-4 w-4" />
      Import
    </Button>,
    <Button variant="outline" key="export">
      <Download className="mr-2 h-4 w-4" />
      Export
    </Button>,
    <Button variant="outline" key="add-workflow" onClick={onAddWorkflow}>
      <Plus className="mr-2 h-4 w-4" />
      Add Workflow
    </Button>,
    <Button key="save" onClick={onSave}>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </Button>,
  ],
});
