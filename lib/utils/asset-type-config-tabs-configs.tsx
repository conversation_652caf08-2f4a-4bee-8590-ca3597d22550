import { HeaderTabItem } from "@/types/header";
import { 
  FileText, 
  Settings, 
  GitBranch, 
  Wrench, 
  TrendingDown, 
  Workflow,
  Info,
  History
} from "lucide-react";

// Asset Type Forms Configuration Tabs
export const getAssetTypeFormsHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Form configuration overview and summary"
  },
  {
    id: "create-form",
    label: "Asset Creation",
    icon: FileText,
    description: "Configure form for asset creation"
  },
  {
    id: "update-form",
    label: "Asset Update",
    icon: Settings,
    description: "Configure form for asset updates"
  },
  {
    id: "maintenance-form",
    label: "Maintenance Log",
    icon: Wrench,
    description: "Configure form for maintenance logging"
  },
  {
    id: "disposal-form",
    label: "Asset Disposal",
    icon: TrendingDown,
    description: "Configure form for asset disposal"
  },
  {
    id: "transfer-form",
    label: "Asset Transfer",
    icon: GitBranch,
    description: "Configure form for asset transfers"
  }
];

// Custom Fields Configuration Tabs
export const getCustomFieldsHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Custom fields overview and summary"
  },
  {
    id: "basic-fields",
    label: "Basic Fields",
    icon: FileText,
    description: "Configure basic custom fields"
  },
  {
    id: "advanced-fields",
    label: "Advanced Fields",
    icon: Settings,
    description: "Configure advanced field types and validation"
  },
  {
    id: "field-groups",
    label: "Field Groups",
    icon: GitBranch,
    description: "Organize fields into logical groups"
  },
  {
    id: "conditional-logic",
    label: "Conditional Logic",
    icon: Workflow,
    description: "Configure field dependencies and conditions"
  }
];

// Lifecycle Stages Configuration Tabs
export const getLifecycleStagesHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Lifecycle stages overview and flow"
  },
  {
    id: "stages",
    label: "Stages",
    icon: GitBranch,
    description: "Configure lifecycle stages"
  },
  {
    id: "transitions",
    label: "Transitions",
    icon: Workflow,
    description: "Configure stage transitions and rules"
  },
  {
    id: "automation",
    label: "Automation",
    icon: Settings,
    description: "Configure automated actions and notifications"
  },
  {
    id: "history",
    label: "History",
    icon: History,
    description: "View lifecycle configuration history"
  }
];

// Maintenance Schedules Configuration Tabs
export const getMaintenanceSchedulesHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Maintenance schedules overview"
  },
  {
    id: "preventive",
    label: "Preventive",
    icon: Wrench,
    description: "Configure preventive maintenance schedules"
  },
  {
    id: "predictive",
    label: "Predictive",
    icon: TrendingDown,
    description: "Configure predictive maintenance schedules"
  },
  {
    id: "corrective",
    label: "Corrective",
    icon: Settings,
    description: "Configure corrective maintenance schedules"
  },
  {
    id: "templates",
    label: "Templates",
    icon: FileText,
    description: "Manage maintenance schedule templates"
  }
];

// Depreciation Settings Configuration Tabs
export const getDepreciationSettingsHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Depreciation settings overview"
  },
  {
    id: "methods",
    label: "Methods",
    icon: TrendingDown,
    description: "Configure depreciation methods"
  },
  {
    id: "parameters",
    label: "Parameters",
    icon: Settings,
    description: "Configure depreciation parameters"
  },
  {
    id: "calculation",
    label: "Calculation",
    icon: FileText,
    description: "View calculation examples and preview"
  },
  {
    id: "history",
    label: "History",
    icon: History,
    description: "View depreciation settings history"
  }
];

// Workflows Configuration Tabs
export const getWorkflowsHeaderTabs = (): HeaderTabItem[] => [
  {
    id: "overview",
    label: "Overview",
    icon: Info,
    description: "Workflows overview and summary"
  },
  {
    id: "automation",
    label: "Automation",
    icon: Workflow,
    description: "Configure automated workflows"
  },
  {
    id: "triggers",
    label: "Triggers",
    icon: Settings,
    description: "Configure workflow triggers"
  },
  {
    id: "actions",
    label: "Actions",
    icon: GitBranch,
    description: "Configure workflow actions"
  },
  {
    id: "monitoring",
    label: "Monitoring",
    icon: History,
    description: "Monitor workflow executions"
  }
];
